// services/netgsmService.js
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

// NETGSM credentials from .env file
const NETGSM_USERNAME = process.env.NETGSM_USERNAME;
const NETGSM_PASSWORD = process.env.NETGSM_PASSWORD;
const NETGSM_HEADER = process.env.NETGSM_HEADER;

// Debug: Log credentials (hide password)
console.log('NETGSM Config Check:');
console.log('Username:', NETGSM_USERNAME ? 'SET' : 'MISSING');
console.log('Password:', NETGSM_PASSWORD ? 'SET' : 'MISSING'); 
console.log('Header:', NETGSM_HEADER ? NETGSM_HEADER : 'MISSING');

/**
 * Format Turkish phone numbers to E.164 (international) format.
 */
const formatPhoneNumber = (phoneNumber) => {
  const cleanNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');
  
  if (cleanNumber.startsWith('0')) {
    return '90' + cleanNumber.slice(1);
  } else if (cleanNumber.startsWith('5') && cleanNumber.length === 10) {
    return '90' + cleanNumber;
  } else if (cleanNumber.startsWith('90')) {
    return cleanNumber;
  } else {
    return '90' + cleanNumber;
  }
};

/**
 * Validate if the given number matches Turkish mobile patterns.
 */
const isValidTurkishNumber = (phoneNumber) => {
  const cleanNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');

  const patterns = [
    /^5\d{9}$/,     // 5xxxxxxxxx
    /^05\d{9}$/,    // 05xxxxxxxxx
    /^905\d{9}$/    // 905xxxxxxxxx
  ];

  return patterns.some((pattern) => pattern.test(cleanNumber));
};

/**
 * Helper function to convert sendOtp response to ApiError
 * @param {Object} smsResult - The result from sendOtp function
 * @returns {Object} Object with statusCode and message for ApiError
 */
export const handleSmsError = (smsResult) => {
  let errorMessage = "Failed to send verification code. Please try again.";
  let statusCode = 500;

  switch (smsResult.error) {
    case 'CONFIGURATION_ERROR':
      errorMessage = "SMS service is temporarily unavailable. Please contact support.";
      statusCode = 503;
      break;
    case 'VALIDATION_ERROR':
      errorMessage = smsResult.message;
      statusCode = 400;
      break;
    case 'INVALID_PHONE_NUMBER':
      errorMessage = smsResult.message;
      statusCode = 400;
      break;
    case 'NETGSM_API_ERROR':
      if (smsResult.details?.errorCode === '60') {
        errorMessage = "SMS service is temporarily unavailable due to insufficient credits. Please contact support.";
        statusCode = 503;
      } else if (smsResult.details?.errorCode === '30') {
        errorMessage = "SMS service authentication failed. Please contact support.";
        statusCode = 503;
      } else {
        errorMessage = `SMS service error: ${smsResult.message}`;
        statusCode = 503;
      }
      break;
    case 'CONNECTION_REFUSED':
    case 'DNS_ERROR':
    case 'TIMEOUT_ERROR':
      errorMessage = "SMS service is temporarily unavailable. Please try again later.";
      statusCode = 503;
      break;
    default:
      errorMessage = `Failed to send verification code: ${smsResult.message}`;
      statusCode = 500;
  }

  return { statusCode, errorMessage };
};

/**
 * Send OTP SMS using NETGSM API
 * @param {string} phoneNumber - The phone number to send OTP to
 * @param {string} verificationCode - The OTP code to send
 * @returns {Object} Response object with success status and details
 */
export const sendOtp = async (phoneNumber, verificationCode) => {
  try {
    console.log('\n=== NETGSM SMS SENDING DEBUG ===');
    console.log('Input phone number:', phoneNumber);
    console.log('Verification code:', verificationCode);

    // Step 1: Check environment variables
    if (!NETGSM_USERNAME || !NETGSM_PASSWORD || !NETGSM_HEADER) {
      return {
        success: false,
        error: 'CONFIGURATION_ERROR',
        message: 'NETGSM service is not properly configured. Missing credentials in environment variables.',
        details: {
          missingCredentials: {
            username: !NETGSM_USERNAME,
            password: !NETGSM_PASSWORD,
            header: !NETGSM_HEADER
          }
        }
      };
    }

    // Step 2: Validate input parameters
    if (!phoneNumber || !verificationCode) {
      return {
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Phone number and verification code are required.',
        details: {
          missingFields: {
            phoneNumber: !phoneNumber,
            verificationCode: !verificationCode
          }
        }
      };
    }

    // Step 3: Validate phone number format
    if (!isValidTurkishNumber(phoneNumber)) {
      return {
        success: false,
        error: 'INVALID_PHONE_NUMBER',
        message: `Invalid phone number format: ${phoneNumber}. Please use Turkish mobile format like 05xxxxxxxxx.`,
        details: {
          providedNumber: phoneNumber,
          expectedFormat: '05xxxxxxxxx or 5xxxxxxxxx'
        }
      };
    }

    // Step 4: Format number
    const formattedNumber = formatPhoneNumber(phoneNumber);
    console.log('Formatted Number:', formattedNumber);

    // Step 5: Create message
    const message = `Dogrulama kodunuz: ${verificationCode}`;
    console.log('Message:', message);

    // Step 6: Create payload for NETGSM using environment variables
    const payload = new URLSearchParams({
      usercode: NETGSM_USERNAME,
      password: NETGSM_PASSWORD,
      gsmno: formattedNumber,
      message: message,
      msgheader: NETGSM_HEADER
    });

    console.log('Payload (credentials hidden):', {
      usercode: NETGSM_USERNAME ? '[SET]' : '[MISSING]',
      password: '[HIDDEN]',
      gsmno: formattedNumber,
      message: message,
      msgheader: NETGSM_HEADER
    });

    // Step 7: Send POST request via axios
    console.log('Sending request to NETGSM...');
    const response = await axios.post(
      'https://api.netgsm.com.tr/sms/send/get',
      payload,
      {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 15000 // 15 second timeout (increased for better reliability)
      }
    );

    console.log('NETGSM Raw Response:', response.data);
    console.log('Response Status:', response.status);

    // Step 8: Parse NETGSM response
    const responseText = response.data.toString().trim();

    if (responseText.startsWith('00')) {
      const parts = responseText.split(' ');
      return {
        success: true,
        messageId: parts[1] || 'unknown',
        response: responseText,
        formattedNumber: formattedNumber,
        message: 'SMS sent successfully'
      };
    } else {
      // Enhanced NETGSM error codes mapping
      const errorMessages = {
        '20': 'Message text is empty or invalid',
        '30': 'Invalid username or password credentials',
        '40': 'Message header (sender name) not defined or invalid',
        '50': 'Invalid recipient phone number format',
        '60': 'Insufficient SMS credits in account',
        '70': 'Invalid or unauthorized message header',
        '80': 'Message contains forbidden content',
        '85': 'Invalid message encoding',
        '90': 'System maintenance in progress'
      };

      const errorCode = responseText.substring(0, 2);
      const errorMsg = errorMessages[errorCode] || `Unknown NETGSM error: ${responseText}`;

      return {
        success: false,
        error: 'NETGSM_API_ERROR',
        message: errorMsg,
        details: {
          errorCode: errorCode,
          fullResponse: responseText,
          formattedNumber: formattedNumber
        }
      };
    }

  } catch (error) {
    console.error('\n=== NETGSM ERROR DEBUG ===');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);

    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    }

    if (error.code) {
      console.error('Error code:', error.code);
    }

    // Return structured error response instead of throwing
    if (error.response) {
      return {
        success: false,
        error: 'HTTP_ERROR',
        message: `NETGSM API returned HTTP ${error.response.status}: ${error.response.data || 'Unknown error'}`,
        details: {
          httpStatus: error.response.status,
          responseData: error.response.data,
          url: 'https://api.netgsm.com.tr/sms/send/get'
        }
      };
    } else if (error.code === 'ECONNREFUSED') {
      return {
        success: false,
        error: 'CONNECTION_REFUSED',
        message: 'Cannot connect to NETGSM API. The service may be down or there may be a network issue.',
        details: {
          errorCode: error.code,
          suggestion: 'Check your internet connection and try again later.'
        }
      };
    } else if (error.code === 'ENOTFOUND') {
      return {
        success: false,
        error: 'DNS_ERROR',
        message: 'NETGSM API endpoint not found. There may be a DNS or network configuration issue.',
        details: {
          errorCode: error.code,
          url: 'https://api.netgsm.com.tr/sms/send/get'
        }
      };
    } else if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'TIMEOUT_ERROR',
        message: 'Request to NETGSM API timed out. The service may be slow or unavailable.',
        details: {
          errorCode: error.code,
          timeout: '15 seconds'
        }
      };
    } else {
      return {
        success: false,
        error: 'UNKNOWN_ERROR',
        message: `Failed to send OTP: ${error.message}`,
        details: {
          originalError: error.message,
          errorType: error.constructor.name
        }
      };
    }
  }
};