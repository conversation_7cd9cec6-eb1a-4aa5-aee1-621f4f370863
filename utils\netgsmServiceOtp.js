// services/netgsmService.js
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

// NETGSM credentials from .env file
const NETGSM_USERNAME = process.env.NETGSM_USERNAME;
const NETGSM_PASSWORD = process.env.NETGSM_PASSWORD;
const NETGSM_HEADER = process.env.NETGSM_HEADER;

// Debug: Log credentials (hide password)
console.log('NETGSM Config Check:');
console.log('Username:', NETGSM_USERNAME ? 'SET' : 'MISSING');
console.log('Password:', NETGSM_PASSWORD ? 'SET' : 'MISSING'); 
console.log('Header:', NETGSM_HEADER ? NETGSM_HEADER : 'MISSING');

/**
 * Format Turkish phone numbers to E.164 (international) format.
 */
const formatPhoneNumber = (phoneNumber) => {
  const cleanNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');
  
  if (cleanNumber.startsWith('0')) {
    return '90' + cleanNumber.slice(1);
  } else if (cleanNumber.startsWith('5') && cleanNumber.length === 10) {
    return '90' + cleanNumber;
  } else if (cleanNumber.startsWith('90')) {
    return cleanNumber;
  } else {
    return '90' + cleanNumber;
  }
};

/**
 * Validate if the given number matches Turkish mobile patterns.
 */
const isValidTurkishNumber = (phoneNumber) => {
  const cleanNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');

  const patterns = [
    /^5\d{9}$/,     // 5xxxxxxxxx
    /^05\d{9}$/,    // 05xxxxxxxxx
    /^905\d{9}$/    // 905xxxxxxxxx
  ];

  return patterns.some((pattern) => pattern.test(cleanNumber));
};

/**
 * Send OTP SMS using NETGSM API
 */
export const sendOtp = async (phoneNumber, verificationCode) => {
  try {
    console.log('\n=== NETGSM SMS SENDING DEBUG ===');
    console.log('Input phone number:', phoneNumber);
    console.log('Verification code:', verificationCode);

    // Step 1: Check environment variables
    if (!NETGSM_USERNAME || !NETGSM_PASSWORD || !NETGSM_HEADER) {
      throw new Error('Missing NETGSM credentials in environment variables');
    }

    // Step 2: Validate number
    if (!isValidTurkishNumber(phoneNumber)) {
      throw new Error(`Invalid phone number format: ${phoneNumber}. Please use format like 05xxxxxxxxx`);
    }

    // Step 3: Format number
    const formattedNumber = formatPhoneNumber(phoneNumber);
    console.log('Formatted Number:', formattedNumber);

    // Step 4: Create message
    const message = `Dogrulama kodunuz: ${verificationCode}`;
    console.log('Message:', message);

    // Step 5: Create payload for NETGSM
    const payload = new URLSearchParams({
      usercode: "08503091122",
      password: "Kaan_1905*",
      gsmno: formattedNumber,
      message: message,
      msgheader: "Expertise"
    });

    console.log('Payload:', payload.toString());

    // Step 6: Send POST request via axios
    console.log('Sending request to NETGSM...');
    const response = await axios.post(
      'https://api.netgsm.com.tr/sms/send/get',
      payload,
      { 
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 10000 // 10 second timeout
      }
    );

    console.log('NETGSM Raw Response:', response.data);
    console.log('Response Status:', response.status);

    // Step 7: Parse NETGSM response
    const responseText = response.data.toString().trim();
    
    if (responseText.startsWith('00')) {
      const parts = responseText.split(' ');
      return {
        success: true,
        messageId: parts[1] || 'unknown',
        response: responseText,
        formattedNumber: formattedNumber
      };
    } else {
      // Common NETGSM error codes
      const errorMessages = {
        '20': 'Message text is empty',
        '30': 'Invalid username or password',
        '40': 'Message header (sender name) not defined',
        '50': 'Invalid recipient number',
        '60': 'Insufficient credits',
        '70': 'Invalid message header'
      };
      
      const errorCode = responseText.substring(0, 2);
      const errorMsg = errorMessages[errorCode] || `Unknown error: ${responseText}`;
      
      throw new Error(`NETGSM Error ${errorCode}: ${errorMsg}`);
    }

  } catch (error) {
    console.error('\n=== NETGSM ERROR DEBUG ===');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    
    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    }
    
    if (error.code) {
      console.error('Error code:', error.code);
    }

    // Re-throw with more specific error
    if (error.response) {
      throw new Error(`NETGSM API Error (${error.response.status}): ${error.response.data}`);
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('Cannot connect to NETGSM API. Check your internet connection.');
    } else if (error.code === 'ENOTFOUND') {
      throw new Error('NETGSM API endpoint not found. Check the URL.');
    } else {
      throw new Error(`Failed to send OTP: ${error.message}`);
    }
  }
};