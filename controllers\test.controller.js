import ApiResponse from "../utils/ApiResponse.js";
import asyncHandler from "../utils/asyncHandler.js";
import { testNetgsmConfig } from "../utils/testNetgsm.js";

/**
 * Test endpoint for NETGSM configuration
 * GET /api/v1/test/netgsm
 */
export const testNetgsm = asyncHandler(async (req, res) => {
  const result = await testNetgsmConfig();
  
  return res
    .status(result.success ? 200 : 500)
    .json(
      new ApiResponse(
        result.success ? 200 : 500,
        result,
        result.success ? "NETGSM test completed successfully" : "NETGSM test failed"
      )
    );
});
