import ApiResponse from "../utils/ApiResponse.js";
import asyncHandler from "../utils/asyncHandler.js";
import { testNetgsmConfig } from "../utils/testNetgsm.js";
import axios from 'axios';

/**
 * Get server IP information
 * GET /api/v1/test/ip
 */
export const getServerIP = asyncHandler(async (req, res) => {
  try {
    const publicIPResponse = await axios.get('https://api.ipify.org?format=json', { timeout: 5000 });
    const publicIP = publicIPResponse.data.ip;

    return res.status(200).json(
      new ApiResponse(200, {
        publicIP: publicIP,
        requestIP: req.ip || req.connection.remoteAddress,
        forwardedFor: req.headers['x-forwarded-for'],
        userAgent: req.headers['user-agent'],
        instructions: "Add the publicIP to your NETGSM account IP whitelist"
      }, "Server IP information retrieved successfully")
    );
  } catch (error) {
    return res.status(500).json(
      new ApiResponse(500, {
        error: error.message,
        requestIP: req.ip || req.connection.remoteAddress,
        forwardedFor: req.headers['x-forwarded-for']
      }, "Failed to get public IP")
    );
  }
});

/**
 * Test endpoint for NETGSM configuration
 * GET /api/v1/test/netgsm
 */
export const testNetgsm = asyncHandler(async (req, res) => {
  const result = await testNetgsmConfig();

  return res
    .status(result.success ? 200 : 500)
    .json(
      new ApiResponse(
        result.success ? 200 : 500,
        result,
        result.success ? "NETGSM test completed successfully" : "NETGSM test failed"
      )
    );
});
