// Simple script to get your server's public IP address
import axios from 'axios';

const getPublicIP = async () => {
  try {
    console.log('🔍 Getting your server\'s public IP address...\n');
    
    // Try multiple IP services for reliability
    const services = [
      'https://api.ipify.org?format=json',
      'https://httpbin.org/ip',
      'https://api.myip.com',
      'https://ipapi.co/json'
    ];
    
    for (const service of services) {
      try {
        console.log(`Trying ${service}...`);
        const response = await axios.get(service, { timeout: 5000 });
        
        let ip;
        if (service.includes('ipify')) {
          ip = response.data.ip;
        } else if (service.includes('httpbin')) {
          ip = response.data.origin;
        } else if (service.includes('myip')) {
          ip = response.data.ip;
        } else if (service.includes('ipapi')) {
          ip = response.data.ip;
        }
        
        if (ip) {
          console.log(`✅ Success! Your server's public IP is: ${ip}\n`);
          console.log('📋 COPY THIS IP ADDRESS: ' + ip);
          console.log('📝 Add this IP to your NETGSM account whitelist\n');
          
          console.log('🔗 NETGSM Panel Steps:');
          console.log('1. Go to https://www.netgsm.com.tr');
          console.log('2. Login to your account');
          console.log('3. Look for "API Ayarları" or "API Settings"');
          console.log('4. Find "IP Yetkilendirme" or "IP Authorization"');
          console.log('5. Add this IP: ' + ip);
          console.log('6. Save the changes');
          console.log('7. Wait 5-10 minutes for changes to take effect\n');
          
          return ip;
        }
      } catch (error) {
        console.log(`❌ Failed: ${error.message}`);
        continue;
      }
    }
    
    throw new Error('All IP services failed');
    
  } catch (error) {
    console.error('❌ Could not determine public IP:', error.message);
    console.log('\n🔧 Alternative methods:');
    console.log('1. Visit https://whatismyipaddress.com in your browser');
    console.log('2. Run: curl ifconfig.me');
    console.log('3. Run: curl ipinfo.io/ip');
    return null;
  }
};

// Run the function
getPublicIP()
  .then(ip => {
    if (ip) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
