// Test script for NETGSM configuration
import { sendOtp } from './netgsmServiceOtp.js';

/**
 * Test function to verify NETGSM configuration
 * This helps debug authentication issues
 */
export const testNetgsmConfig = async () => {
  console.log('\n=== TESTING NETGSM CONFIGURATION ===');
  
  try {
    // Test with a dummy phone number and OTP
    const testPhoneNumber = '05234574323'; // Use the same number from your request
    const testOtp = '123456';
    
    console.log('Testing with:');
    console.log('Phone:', testPhoneNumber);
    console.log('OTP:', testOtp);
    
    const result = await sendOtp(testPhoneNumber, testOtp);
    
    console.log('\n=== TEST RESULT ===');
    console.log('Success:', result.success);
    console.log('Error:', result.error || 'None');
    console.log('Message:', result.message);
    
    if (result.details) {
      console.log('Details:', JSON.stringify(result.details, null, 2));
    }
    
    return result;
    
  } catch (error) {
    console.error('\n=== TEST ERROR ===');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    return {
      success: false,
      error: 'TEST_ERROR',
      message: error.message
    };
  }
};

// If running this file directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testNetgsmConfig()
    .then(result => {
      console.log('\n=== FINAL RESULT ===');
      console.log(JSON.stringify(result, null, 2));
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}
